# 历史数据迁移方案

## 1. 项目目标

将合作风投机构的存量数据，包括 **结构化数据** 和 **商业计划书附件**，高效、准确地迁移并整合进我们的平台，构建统一、丰富的知识库。

## 2. 核心功能模块设计

### 模块一：结构化数据迁移与对齐
- **目标**：将合作方数据库中的公司字段数据，与我们系统的数据模型进行对齐和迁移。
- **关键活动**：
    - 数据库Schema对齐与字段映射。
    - 开发自动化数据迁移脚本。
    - 设定数据冲突处理规则。

### 模块二：非结构化数据处理 (AI核心)
- **目标**：从商业计划书等附件中，利用AI技术提取关键信息，并将其转化为结构化数据。
- **关键活动**：
    - 开发支持多格式（PDF, Word, PPT）的文档批量解析引擎。
    - 利用NLP模型，自动抽取核心商业信息（公司简介、技术特点、团队背景、融资需求等）。
    - 将抽取出的结构化信息，自动填充到我们数据库中对应的公司字段下。

### 模块三：数据补充与增强
- **目标**：利用外部数据源，对迁移过来的数据进行信息补充和交叉验证。
- **关键活动**：
    - 调用外部API（如企查查）补充缺失的工商、融资信息。
    - 通过网络爬虫抓取公开信息进行验证和丰富。
    - 对多源数据进行智能融合与去重。

## 3. 数据迁移整体流程

```mermaid
graph TD
    %% 输入
    subgraph "输入源 (合作方数据)"
        A1[关系型数据库]
        A2[商业计划书附件]
    end

    %% 处理引擎
    subgraph "数据迁移与处理引擎"
        B1[模块一：结构化数据迁移与对齐]
        B2["模块二：非结构化数据处理 (AI抽取)"]
        B3[模块三：数据补充与增强]
    end

    %% 融合与输出
    subgraph "数据融合与入库"
        C1[数据清洗、验证与去重]
        C2[统一数据模型]
        D1[(我们的统一知识库)]
    end

    %% 流程
    A1 --> B1 --> C1
    A2 --> B2 --> C1
    C1 --> B3 --> C1
    C1 --> C2 --> D1

    %% 样式
    classDef input fill:#fce4ec
    classDef engine fill:#e3f2fd
    classDef output fill:#e8f5e8
    
    class A1,A2 input
    class B1,B2,B3 engine
    class C1,C2,D1 output
```

## 4. 预期成果

- **完成数据迁移**：将合作机构的存量公司数据完整迁移至新平台。
- **构建统一知识库**：形成一个经过对齐、增强和去重的统一公司知识库。
- **释放数据价值**：使原本沉睡在附件中的商业计划书信息，转化为可被AI检索和分析的结构化数据。

---
*本方案作为产品设计总览下的一个关键项目，将由产品经理和技术团队进一步细化执行。*
