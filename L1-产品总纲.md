# 产品总纲 (Product Charter)

## 1. 使命 (Mission)

**构建中国领先的智能化一级市场投资决策平台，通过分层架构整合3000万+公司数据库与AI分析能力，为风险投资机构提供从Deal Sourcing到投后管理的全流程解决方案，重新定义一级市场投资的效率与精度。**

---

## 2. 产品定位 (Product Positioning)

- **市场对标**：对标美国Harmonic.ai，打造中国一级市场的智能投资平台
- **核心优势**：分层架构设计 + 本土化数据整合 + AI中台赋能
- **数据规模**：覆盖3000万+中国公司数据，实时更新融资动态
- **技术特色**：数据中台 + AI中台双驱动，支持传统界面与AI交互并存

---

## 3. 核心用户 (Core Users)

| 用户画像 | 核心诉求 | 主要使用场景 |
| :--- | :--- | :--- |
| **投资分析师 (Analyst)** | 快速筛选、整理海量项目信息，提升早期项目发现效率 | Deal Sourcing、项目初筛、信息收集 |
| **投资经理 (Manager)** | 深入挖掘项目价值，进行多维度尽职调查，撰写高质量投研报告 | 深度投研、尽调报告、项目跟进 |
| **合伙人 (Partner)** | 宏观掌握赛道动态，高效管理项目管线，进行快速、准确的投资决策 | 投资决策、团队协作、投后管理 |

---

## 4. 价值支柱 (Value Pillars)

1. **AI驱动的智能发现 (AI-Powered Discovery)**
   - **目标**：通过AI技术主动发现投资机会，而非被动等待项目上门
   - **价值**：提前6-12个月识别潜在标的，获得投资先机

2. **全景数据整合 (Comprehensive Data Integration)**
   - **目标**：通过数据中台整合公开数据、私域数据和第三方数据，构建最完整的项目视图
   - **价值**：消除信息盲点，提供360度项目洞察

3. **AI中台赋能 (AI Platform Empowerment)**
   - **目标**：通过AI中台为投研流程提供智能化支持，从信息筛选到分析报告
   - **价值**：将投研效率提升10倍，让用户专注于价值判断

4. **协同决策支持 (Collaborative Decision Support)**
   - **目标**：支持团队协作和知识沉淀，形成组织投资智慧
   - **价值**：提升决策质量，降低投资风险

---

## 5. 产品功能架构 (Product Architecture)

### 5.1 核心功能模块

```mermaid
graph TD
    subgraph "应用层 (Applications)"
        APP1["Deal Sourcing<br/>(项目发现与监控)"]
        APP2["深度投研<br/>(项目分析与尽调)"]
        APP3["投资管理<br/>(项目管线与决策)"]
        APP4["团队协作<br/>(CRM与知识管理)"]
    end

    subgraph "AI中台 (AI Platform)"
        A1[AI Chat 助手]
        A2[智能推荐与筛选引擎]
        A3[自动化分析与报告引擎]
    end

    subgraph "数据中台 (Data Platform)"
        DP1["数据抽取、清洗、整合"]
        DP2["核心数据库<br/>(3000万+公司库)"]
    end

    subgraph "数据源层 (Data Sources)"
        D1["公开数据<br/>(API, 网络爬虫)"]
        D2["私域数据<br/>(CRM, 文件导入)"]
        D3["第三方数据"]
    end

    %% 核心数据流
    D1 & D2 & D3 --> DP1
    DP1 --> DP2
    
    %% 数据中台向上支撑
    DP2 --> A1 & A2 & A3
    
    %% 应用层直接使用数据
    DP2 --> APP1 & APP3 & APP4
    
    %% AI中台赋能关键应用
    A2 --> APP1
    A3 --> APP2
```

### 5.2 分层架构说明

| 层级 | 模块 | 核心功能 | 关键价值 |
| :--- | :--- | :--- | :--- |
| **数据源层** | 公开数据/私域数据/第三方数据 | 多源数据接入与采集 | 构建全景数据基础 |
| **数据中台** | 数据处理/核心数据库 | 数据清洗、整合、存储 | 将原始数据转化为业务资产 |
| **AI中台** | Chat助手/推荐引擎/分析引擎 | 智能能力抽象与复用 | 为上层应用提供AI赋能 |
| **应用层** | Deal Sourcing/投研/管理/协作 | 具体业务场景实现 | 直接服务用户业务需求 |

### 5.3 关键数据流向
- **数据汇聚**：多源数据 → 数据中台统一处理
- **能力构建**：结构化数据 → AI中台智能分析
- **价值实现**：AI能力 + 数据资产 → 业务应用

---

## 6. 产品体验设计 (User Experience Design)

### 6.1 应用首页布局
- **智能监控面板**：展示关注的赛道、公司动态
- **多维筛选工具**：支持条件筛选与智能推荐
- **搜索入口**：支持关键词搜索和自然语言查询
- **团队协作视图**：查看团队成员的项目进展

### 6.2 核心交互模式
- **多元交互**：传统筛选界面与AI Chat助手结合
- **可视化呈现**：通过图表展示关键信息
- **分层深入**：从概览逐步深入到详细分析
- **协作支持**：显示团队成员的操作状态

### 6.3 关键页面设计
- **公司详情页**：基本信息、融资历史、团队背景，辅以AI分析洞察
- **项目管线页**：展示项目状态和决策流程
- **搜索结果页**：智能排序和筛选功能
- **协作工作台**：任务分配和进度跟踪

---

## 7. 设计原则 (Design Principles)

### 7.1 分层架构优先
- **数据中台**：统一数据标准，保证数据质量和一致性
- **AI中台**：能力复用，避免重复建设
- **应用分离**：业务逻辑与底层能力解耦，支持快速迭代

### 7.2 数据驱动决策
- **全量数据**：整合所有可获得的相关数据源
- **实时更新**：确保数据的时效性和准确性
- **智能关联**：通过AI发现数据间的隐藏关系

### 7.3 用户体验至上
- **多元交互**：支持传统界面与AI Chat并存，满足不同用户习惯
- **个性定制**：根据用户角色和偏好定制界面和功能
- **响应迅速**：确保系统响应速度和稳定性

### 7.4 协作透明化
- **信息共享**：团队成员可见彼此的工作进展
- **知识沉淀**：将个人经验转化为组织知识
- **决策可追溯**：记录决策过程和依据

---

## 8. 成功指标 (Success Metrics)

### 8.1 用户价值指标
- **项目发现效率**：用户筛选项目的时间缩短比例
- **投研工作效率**：单个项目分析时间缩短比例
- **平台采用率**：团队成员的活跃使用程度

### 8.2 产品使用指标
- **用户活跃度**：日活用户数和使用时长
- **功能采用率**：各核心功能的使用频率
- **协作效率**：团队协作功能的使用深度

---

*本文档作为L1战略层文档，为产品开发提供总体方向指导。具体功能实现将在L2功能规格文档中详细定义。*
